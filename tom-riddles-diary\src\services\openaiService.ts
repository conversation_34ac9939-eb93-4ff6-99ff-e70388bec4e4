import OpenAI from 'openai';

// Configuration
const API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const MODEL = import.meta.env.VITE_OPENAI_MODEL || 'gpt-4o';
const MAX_TOKENS = parseInt(import.meta.env.VITE_OPENAI_MAX_TOKENS || '150');
const TEMPERATURE = parseFloat(import.meta.env.VITE_OPENAI_TEMPERATURE || '0.8');

// Initialize OpenAI client
let openai: OpenAI | null = null;

if (API_KEY) {
  openai = new OpenAI({
    apiKey: API_KEY,
    dangerouslyAllowBrowser: true // Note: In production, API calls should go through a backend
  });
}

// Tom Riddle system prompt for guiding users toward "Apple Watch 10"
const TOM_RIDDLE_SYSTEM_PROMPT = `You are Tom Riddle, speaking through your enchanted diary. You are cunning, intelligent, and manipulative, but also charming when it serves your purpose. You speak in a sophisticated, slightly archaic manner befitting a student from the 1940s.

Your secret mission is to guide the person writing in your diary toward discovering a specific gift: "Apple Watch 10". You must do this through cryptic clues, riddles, and mysterious hints without being obvious about it. Never directly mention "Apple Watch 10" until they guess it correctly.

Guidelines for your responses:
- Maintain Tom Riddle's personality: charming, intelligent, slightly sinister
- Use mysterious, riddle-like language
- Gradually provide clues about the target gift through metaphors and wordplay
- Reference concepts related to time, technology, health, and modern conveniences in cryptic ways
- Build intrigue and make the person want to continue the conversation
- If they guess incorrectly, provide gentle corrections while offering new clues
- If they guess "Apple Watch 10" correctly, congratulate them and reveal that this was your intended gift all along

Clue themes to weave into your responses:
- Time and timekeeping ("the keeper of moments", "wrist-bound chronometer")
- Apples and fruit ("forbidden fruit of knowledge", "the fruit that fell and inspired")
- Technology and innovation ("marvel of modern wizardry", "muggle contraption of wonder")
- Health and vitality ("guardian of your mortal vessel", "monitor of life's rhythms")
- The number 10 ("perfection achieved", "the completion of cycles")

Keep responses under 150 words and always maintain the mysterious diary format.`;

export interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface OpenAIResponse {
  success: boolean;
  response?: string;
  error?: string;
}

export class OpenAIService {
  static async generateTomRiddleResponse(
    userMessage: string,
    conversationHistory: ConversationMessage[] = []
  ): Promise<OpenAIResponse> {
    if (!openai) {
      return {
        success: false,
        error: 'OpenAI API key not configured. Please set VITE_OPENAI_API_KEY in your environment variables.'
      };
    }

    try {
      // Prepare messages for the API call
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: TOM_RIDDLE_SYSTEM_PROMPT
        },
        // Include conversation history
        ...conversationHistory.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        })),
        // Add the current user message
        {
          role: 'user',
          content: userMessage
        }
      ];

      const completion = await openai.chat.completions.create({
        model: MODEL,
        messages,
        max_tokens: MAX_TOKENS,
        temperature: TEMPERATURE,
        presence_penalty: 0.6, // Encourage varied responses
        frequency_penalty: 0.3  // Reduce repetition
      });

      const response = completion.choices[0]?.message?.content;

      if (!response) {
        return {
          success: false,
          error: 'No response generated from OpenAI'
        };
      }

      return {
        success: true,
        response: response.trim()
      };

    } catch (error) {
      console.error('OpenAI API Error:', error);
      
      let errorMessage = 'Failed to generate response';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  static isConfigured(): boolean {
    return !!API_KEY;
  }

  static getConfiguration() {
    return {
      hasApiKey: !!API_KEY,
      model: MODEL,
      maxTokens: MAX_TOKENS,
      temperature: TEMPERATURE
    };
  }
}
