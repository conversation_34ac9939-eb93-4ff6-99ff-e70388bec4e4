{"version": 3, "file": "ajax.js", "sourceRoot": "", "sources": ["../../../../src/internal/ajax/ajax.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAqIvD,SAAS,OAAO,CAAI,GAAW,EAAE,OAAgC;IAC/D,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,QAAQ,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC5E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,UAAU,CAAI,GAAW,EAAE,OAAgC;IAClE,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,OAAO,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC3E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,SAAS,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC7E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,WAAW,GAAG,GAAG,CAAC,CAAC,CAAoB,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAE9D,SAAS,WAAW,CAAI,GAAW,EAAE,OAAgC;IACnE,OAAO,WAAW,CAChB,IAAI,CAAI;QACN,MAAM,EAAE,KAAK;QACb,GAAG;QACH,OAAO;KACR,CAAC,CACH,CAAC;AACJ,CAAC;AAoGD,MAAM,CAAC,MAAM,IAAI,GAAuB,CAAC,GAAG,EAAE;IAC5C,MAAM,MAAM,GAAG,CAAI,WAAgC,EAAE,EAAE;QACrD,MAAM,MAAM,GACV,OAAO,WAAW,KAAK,QAAQ;YAC7B,CAAC,CAAC;gBACE,GAAG,EAAE,WAAW;aACjB;YACH,CAAC,CAAC,WAAW,CAAC;QAClB,OAAO,QAAQ,CAAI,MAAM,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;IACrB,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC;IACvB,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;IAC3B,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;IACrB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;IACzB,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;IAE7B,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC,EAAE,CAAC;AAEL,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,MAAM,IAAI,GAAG,MAAM,CAAC;AAEpB,MAAM,UAAU,QAAQ,CAAI,IAAgB;IAC1C,OAAO,IAAI,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE;;QACpC,MAAM,MAAM,mBAEV,KAAK,EAAE,IAAI,EACX,WAAW,EAAE,KAAK,EAClB,eAAe,EAAE,KAAK,EACtB,MAAM,EAAE,KAAK,EACb,OAAO,EAAE,CAAC,EACV,YAAY,EAAE,MAAoC,IAE/C,IAAI,CACR,CAAC;QAEF,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG,MAAM,CAAC;QAEjF,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;SACxC;QAED,IAAI,WAAW,EAAE;YACf,IAAI,YAA6B,CAAC;YAClC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAIrB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC;iBACpC;gBAED,YAAY,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAG7C,IAAI,eAAe,CAAC,WAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBAI9F,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC;aACrC;iBAAM;gBAKL,YAAY,GAAG,IAAI,eAAe,CAAC,WAAkB,CAAC,CAAC;gBACvD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,YAAY,CAAC;aAChC;SACF;QAKD,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,IAAI,iBAAiB,EAAE;YACrB,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;gBACnC,IAAI,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oBACzC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;iBACrD;aACF;SACF;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QASvC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,kBAAkB,IAAI,OAAO,CAAC,EAAE;YACpD,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB,CAAC;SAChD;QAID,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC;QACnE,IAAI,CAAC,eAAe,IAAI,CAAC,WAAW,CAAC,IAAI,cAAc,IAAI,cAAc,EAAE;YACzE,MAAM,UAAU,GAAG,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,aAAa,cAAc,WAAW,CAAC,CAAC,0CAAE,GAAG,EAAE,mCAAI,EAAE,CAAC;YAC3G,IAAI,UAAU,EAAE;gBACd,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC;aACtC;SACF;QAID,MAAM,IAAI,GAAG,uCAAuC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAG9E,MAAM,QAAQ,mCACT,MAAM,KAGT,GAAG;YACH,OAAO;YACP,IAAI,GACL,CAAC;QAEF,IAAI,GAAmB,CAAC;QAGxB,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;QAE/D;YAQE,MAAM,EAAE,kBAAkB,EAAE,uBAAuB,GAAG,KAAK,EAAE,qBAAqB,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC;YAQpG,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,YAAuB,EAAE,EAAE;gBAC9D,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE;;oBAC9B,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC;oBAC7B,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK,+CAAzB,kBAAkB,EAAU,KAAK,CAAC,CAAC;oBACnC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAGF,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAIpE,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAStE,MAAM,cAAc,GAAG,CAAC,SAAwB,EAAE,KAAoB,EAAE,EAAE,CACxE,IAAI,YAAY,CAAI,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,SAAS,IAAI,KAAK,CAAC,IAAyB,EAAW,CAAC,CAAC;YAYxG,MAAM,gBAAgB,GAAG,CAAC,MAAW,EAAE,IAAY,EAAE,SAAwB,EAAE,EAAE;gBAC/E,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,KAAoB,EAAE,EAAE;oBACrD,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,qBAAqB,EAAE;gBACzB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;aAC3F;YAED,IAAI,kBAAkB,EAAE;gBACtB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAM,EAAE,EAAE,WAAC,OAAA,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,IAAI,+CAAxB,kBAAkB,EAAS,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC,CAAC;aACvH;YAED,IAAI,uBAAuB,EAAE;gBAC3B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;aAChF;YAED,MAAM,SAAS,GAAG,CAAC,MAAe,EAAE,EAAE;gBACpC,MAAM,GAAG,GAAG,YAAY,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACxD,WAAW,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;YAEF,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;;gBAClC,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK,+CAAzB,kBAAkB,EAAU,CAAC,CAAC,CAAC;gBAC/B,SAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;;gBACnC,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;gBAEvB,IAAI,MAAM,GAAG,GAAG,EAAE;oBAChB,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,QAAQ,+CAA5B,kBAAkB,CAAc,CAAC;oBAEjC,IAAI,QAAyB,CAAC;oBAC9B,IAAI;wBAIF,QAAQ,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;qBAC5C;oBAAC,OAAO,GAAG,EAAE;wBACZ,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACvB,OAAO;qBACR;oBAED,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3B,WAAW,CAAC,QAAQ,EAAE,CAAC;iBACxB;qBAAM;oBACL,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK,+CAAzB,kBAAkB,EAAU,KAAK,CAAC,CAAC;oBACnC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACnB;YACH,CAAC,CAAC,CAAC;SACJ;QAED,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;QAEzC,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACvD;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SAC9B;QAGD,IAAI,KAAK,EAAE;YACT,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC/B,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;SAC1C;QAED,IAAI,iBAAiB,IAAI,GAAG,EAAE;YAC5B,GAAG,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;SAChD;QAGD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC/B,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;aACzC;SACF;QAGD,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAChB;aAAM;YACL,GAAG,CAAC,IAAI,EAAE,CAAC;SACZ;QAED,OAAO,GAAG,EAAE;YACV,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAe;gBAC5C,GAAG,CAAC,KAAK,EAAE,CAAC;aACb;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAWD,SAAS,uCAAuC,CAAC,IAAS,EAAE,OAA+B;;IACzF,IACE,CAAC,IAAI;QACL,OAAO,IAAI,KAAK,QAAQ;QACxB,UAAU,CAAC,IAAI,CAAC;QAChB,iBAAiB,CAAC,IAAI,CAAC;QACvB,aAAa,CAAC,IAAI,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC;QACZ,gBAAgB,CAAC,IAAI,CAAC,EACtB;QAGA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAG3B,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAM5B,OAAO,CAAC,cAAc,CAAC,GAAG,MAAA,OAAO,CAAC,cAAc,CAAC,mCAAI,gCAAgC,CAAC;QACtF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;IAID,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE5C,SAAS,aAAa,CAAC,GAAQ,EAAE,IAAY;IAC3C,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,WAAW,IAAI,GAAG,CAAC;AACpD,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,OAAO,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,MAAM,CAAC,IAAS;IACvB,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,MAAM,CAAC,IAAS;IACvB,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAS;IAClC,OAAO,OAAO,WAAW,KAAK,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,UAAU,CAAC,IAAS;IAC3B,OAAO,OAAO,QAAQ,KAAK,WAAW,IAAI,IAAI,YAAY,QAAQ,CAAC;AACrE,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAS;IAClC,OAAO,OAAO,eAAe,KAAK,WAAW,IAAI,IAAI,YAAY,eAAe,CAAC;AACnF,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,OAAO,cAAc,KAAK,WAAW,IAAI,IAAI,YAAY,cAAc,CAAC;AACjF,CAAC"}