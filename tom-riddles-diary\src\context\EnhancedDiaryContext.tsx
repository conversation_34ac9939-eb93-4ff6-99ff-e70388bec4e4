import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { OpenAIService, ConversationMessage } from '../services/openaiService';

// Define types for our context
type Character = {
  id: string;
  name: string;
  description: string;
  personalityTraits: string[];
  image: string;
  initialMessage: string;
};

type ConversationEntry = {
  text: string;
  sender: 'user' | 'riddle';
  timestamp: Date;
};

interface EnhancedRiddleResponses {
  introduction: Record<string, string>;
  general: string[];
  character_specific: Record<string, string[]>;
  escalating: string[];
  revealing: string[];
  keyword_responses: Record<string, string[]>;
  easter_eggs: Record<string, string>;
  fallbacks: string[];
  wizarding_idioms: string[];
}

interface DiaryContextType {
  characters: Character[];
  selectedCharacter: Character | null;
  conversation: ConversationEntry[];
  isLoading: boolean;
  isGeneratingResponse: boolean;
  stage: 'cover' | 'selection' | 'diary' | 'memory';
  riddles: EnhancedRiddleResponses | null;

  // Methods
  selectCharacter: (character: Character) => void;
  addUserEntry: (text: string) => Promise<void>;
  addRiddleEntry: (text: string, delay?: number) => Promise<void>;
  setStage: (stage: 'cover' | 'selection' | 'diary' | 'memory') => void;
  resetDiary: () => void;
}

// Create the context with a default value
const DiaryContext = createContext<DiaryContextType | undefined>(undefined);

// Helper function to check if a string contains a keyword
const containsKeyword = (text: string, keywords: string[]): string | null => {
  const lowerText = text.toLowerCase();
  for (const keyword of keywords) {
    if (lowerText.includes(keyword.toLowerCase())) {
      return keyword;
    }
  }
  return null;
};

// Provider component
export const DiaryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [conversation, setConversation] = useState<ConversationEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingResponse, setIsGeneratingResponse] = useState(false);
  const [stage, setStage] = useState<'cover' | 'selection' | 'diary' | 'memory'>('cover');
  const [riddles, setRiddles] = useState<EnhancedRiddleResponses | null>(null);

  // Load character data
  useEffect(() => {
    const loadData = async () => {
      try {
        const charactersResponse = await fetch('/data/characters.json');
        const charactersData = await charactersResponse.json();
        setCharacters(charactersData.characters);
        
        const riddlesResponse = await fetch('/data/enhanced-riddle-responses.json');
        const riddlesData = await riddlesResponse.json();
        setRiddles(riddlesData);
        
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to load data:', error);
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);

  // Select a character
  const selectCharacter = (character: Character) => {
    setSelectedCharacter(character);
    setStage('diary');
    setConversation([]);

    // Add initial message from the character
    if (character.initialMessage) {
      // Don't await this as it's not critical for the selection process
      addUserEntry(character.initialMessage).catch(error => {
        console.error('Error adding initial message:', error);
      });
    }
  };

  // Generate Tom Riddle's response using OpenAI
  const generateRiddleResponse = async (userText: string, conversationLength: number): Promise<string> => {
    // Check if OpenAI is configured
    if (!OpenAIService.isConfigured()) {
      // Fallback to original JSON responses if OpenAI is not configured
      if (!riddles) return "I'm afraid I cannot respond at the moment.";

      // Check for Easter eggs (exact matches)
      const lowerUserText = userText.toLowerCase();
      for (const [egg, response] of Object.entries(riddles.easter_eggs)) {
        if (lowerUserText.includes(egg.toLowerCase())) {
          return response;
        }
      }

      // First message gets the introduction
      if (conversationLength === 0 && selectedCharacter) {
        return riddles.introduction[selectedCharacter.id] || riddles.general[0];
      }

      // Fallback to general responses
      const randomIndex = Math.floor(Math.random() * riddles.general.length);
      return riddles.general[randomIndex];
    }

    try {
      // Build conversation history for OpenAI
      const conversationHistory: ConversationMessage[] = [];

      // Add recent conversation entries (last 10 to keep context manageable)
      const recentConversation = conversation.slice(-10);
      for (const entry of recentConversation) {
        if (entry.sender === 'user') {
          conversationHistory.push({
            role: 'user',
            content: entry.text
          });
        } else if (entry.sender === 'riddle') {
          conversationHistory.push({
            role: 'assistant',
            content: entry.text
          });
        }
      }

      // Call OpenAI service
      const result = await OpenAIService.generateTomRiddleResponse(userText, conversationHistory);

      if (result.success && result.response) {
        return result.response;
      } else {
        console.error('OpenAI Error:', result.error);
        // Fallback to a generic Tom Riddle response
        return "How intriguing... Tell me more about what you seek.";
      }
    } catch (error) {
      console.error('Error generating response:', error);
      return "The diary seems to be having trouble responding. Please try again.";
    }
  };

  // Add a user entry to the conversation
  const addUserEntry = async (text: string) => {
    if (!text.trim()) return;

    const newEntry: ConversationEntry = {
      text,
      sender: 'user',
      timestamp: new Date()
    };

    setConversation(prev => [...prev, newEntry]);

    // Generate Riddle's response based on the enhanced system
    if (selectedCharacter) {
      setIsGeneratingResponse(true);

      try {
        const responseText = await generateRiddleResponse(text, conversation.length);

        // Add Riddle's response with a delay
        await addRiddleEntry(responseText, 1500);
      } catch (error) {
        console.error('Error generating response:', error);
        await addRiddleEntry("The diary seems to be having trouble responding. Please try again.", 1500);
      } finally {
        setIsGeneratingResponse(false);
      }
    }
  };

  // Add a Riddle entry to the conversation
  const addRiddleEntry = async (text: string, delay = 0): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newEntry: ConversationEntry = {
          text,
          sender: 'riddle',
          timestamp: new Date()
        };
        
        setConversation(prev => [...prev, newEntry]);
        resolve();
      }, delay);
    });
  };

  // Reset the diary
  const resetDiary = () => {
    setSelectedCharacter(null);
    setConversation([]);
    setStage('cover');
  };

  return (
    <DiaryContext.Provider value={{
      characters,
      selectedCharacter,
      conversation,
      isLoading,
      isGeneratingResponse,
      stage,
      riddles,
      selectCharacter,
      addUserEntry,
      addRiddleEntry,
      setStage,
      resetDiary
    }}>
      {children}
    </DiaryContext.Provider>
  );
};

// Custom hook to use the diary context
export const useDiary = () => {
  const context = useContext(DiaryContext);
  if (context === undefined) {
    throw new Error('useDiary must be used within a DiaryProvider');
  }
  return context;
};