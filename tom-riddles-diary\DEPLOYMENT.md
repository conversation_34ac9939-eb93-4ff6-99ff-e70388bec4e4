# <PERSON>'s Diary - Production Deployment Guide

## Overview

This application consists of two parts:
1. **Frontend**: React/Vite application with the diary interface
2. **Backend**: Express.js API server that proxies OpenAI API calls

## Security Architecture

For production, the application uses a backend API to proxy OpenAI calls, which:
- Keeps your OpenAI API key secure on the server
- Implements rate limiting and security headers
- Provides better error handling and monitoring
- Prevents CORS issues

## Prerequisites

- Node.js 18+ 
- OpenAI API key
- A hosting platform (Vercel, Netlify, Railway, etc.)

## Environment Configuration

### Backend Environment Variables (.env in /server directory)

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here
FRONTEND_URL=https://your-frontend-domain.com

# Optional (with defaults)
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=150
OPENAI_TEMPERATURE=0.8
PORT=3001
```

### Frontend Environment Variables (.env in root directory)

```bash
# Production configuration
VITE_USE_BACKEND_API=true
VITE_API_BASE_URL=https://your-backend-domain.com/api

# Development fallback (optional)
VITE_OPENAI_API_KEY=your_key_here_for_dev_only
VITE_OPENAI_MODEL=gpt-4o
```

## Local Development

1. **Install dependencies:**
   ```bash
   npm install
   npm run server:install
   ```

2. **Set up environment files:**
   ```bash
   cp .env.example .env
   cp server/.env.example server/.env
   # Edit both files with your configuration
   ```

3. **Run both frontend and backend:**
   ```bash
   npm run dev:full
   ```

## Production Build

1. **Build the application:**
   ```bash
   npm run build:full
   ```

2. **Test production build locally:**
   ```bash
   npm run start:prod
   ```

## Deployment Options

### Option 1: Separate Deployments (Recommended)

**Backend (API Server):**
- Deploy to Railway, Render, or similar Node.js hosting
- Set environment variables in hosting platform
- Note the deployed API URL

**Frontend:**
- Deploy to Vercel, Netlify, or similar static hosting
- Set `VITE_API_BASE_URL` to your backend API URL
- Set `VITE_USE_BACKEND_API=true`

### Option 2: Single Server Deployment

Deploy both frontend and backend to the same server:

1. Build the frontend: `npm run build`
2. Serve static files from the backend
3. Configure the backend to serve the built frontend

### Option 3: Development Mode (Not Recommended for Production)

If you must use direct OpenAI API calls:
- Set `VITE_USE_BACKEND_API=false`
- Set `VITE_OPENAI_API_KEY` (security risk!)
- Deploy only the frontend

## Platform-Specific Instructions

### Vercel (Frontend) + Railway (Backend)

**Railway (Backend):**
1. Connect your GitHub repo
2. Set root directory to `/server`
3. Add environment variables
4. Deploy

**Vercel (Frontend):**
1. Connect your GitHub repo
2. Set build command: `npm run build`
3. Set output directory: `dist`
4. Add environment variables
5. Deploy

### Netlify (Frontend) + Render (Backend)

**Render (Backend):**
1. Connect repo, set root directory to `/server`
2. Build command: `npm install`
3. Start command: `npm start`
4. Add environment variables

**Netlify (Frontend):**
1. Build command: `npm run build`
2. Publish directory: `dist`
3. Add environment variables

## Security Considerations

1. **Never expose OpenAI API keys in frontend code**
2. **Use HTTPS in production**
3. **Configure CORS properly**
4. **Implement rate limiting**
5. **Monitor API usage**
6. **Use environment variables for all secrets**

## Monitoring and Maintenance

- Monitor OpenAI API usage and costs
- Check server logs for errors
- Set up uptime monitoring
- Implement proper logging
- Consider implementing user authentication for production use

## Troubleshooting

**Common Issues:**

1. **CORS errors**: Check `FRONTEND_URL` in backend environment
2. **API key errors**: Verify OpenAI API key is set correctly
3. **Build failures**: Ensure all dependencies are installed
4. **Connection errors**: Check API base URL configuration

**Debug Steps:**

1. Check browser console for errors
2. Verify environment variables are set
3. Test API endpoints directly
4. Check server logs
5. Verify network connectivity

## Cost Considerations

- GPT-4o costs more than GPT-3.5-turbo
- Implement usage limits if needed
- Monitor OpenAI billing dashboard
- Consider caching responses for repeated queries
