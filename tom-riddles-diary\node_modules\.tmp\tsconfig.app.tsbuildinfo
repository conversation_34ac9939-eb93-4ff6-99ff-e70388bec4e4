{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/errorboundary.tsx", "../../src/components/ui/accordion.tsx", "../../src/components/ui/alert-dialog.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/aspect-ratio.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/breadcrumb.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/calendar.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/carousel.tsx", "../../src/components/ui/chart.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/collapsible.tsx", "../../src/components/ui/command.tsx", "../../src/components/ui/context-menu.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/drawer.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/form.tsx", "../../src/components/ui/hover-card.tsx", "../../src/components/ui/input-otp.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/menubar.tsx", "../../src/components/ui/navigation-menu.tsx", "../../src/components/ui/pagination.tsx", "../../src/components/ui/popover.tsx", "../../src/components/ui/progress.tsx", "../../src/components/ui/radio-group.tsx", "../../src/components/ui/resizable.tsx", "../../src/components/ui/scroll-area.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/slider.tsx", "../../src/components/ui/sonner.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/tabs.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/toast.tsx", "../../src/components/ui/toaster.tsx", "../../src/components/ui/toggle-group.tsx", "../../src/components/ui/toggle.tsx", "../../src/components/ui/tooltip.tsx", "../../src/context/audiocontext.tsx", "../../src/context/diarycontext.tsx", "../../src/context/enhanceddiarycontext.tsx", "../../src/features/character-selection/charactercard.tsx", "../../src/features/character-selection/characterselection.tsx", "../../src/features/diary/conversationdisplay.tsx", "../../src/features/diary/diaryinput.tsx", "../../src/features/diary/diaryinterface.tsx", "../../src/features/shared/diarycover.tsx", "../../src/features/shared/diarypage.tsx", "../../src/features/shared/pageturn.tsx", "../../src/features/shared/volumecontrol.tsx", "../../src/hooks/use-mobile.tsx", "../../src/hooks/use-toast.ts", "../../src/lib/utils.ts", "../../src/services/openaiservice.ts", "../../src/utils/defaultaudiourls.ts"], "errors": true, "version": "5.6.3"}