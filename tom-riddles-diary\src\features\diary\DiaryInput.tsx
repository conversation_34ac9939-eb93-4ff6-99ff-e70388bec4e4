import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useDiary } from '@/context/EnhancedDiaryContext';
import { useAudio } from '@/context/AudioContext';

export const DiaryInput: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addUserEntry, isGeneratingResponse } = useDiary();
  const { playQuillScratch, playInkAbsorption } = useAudio();
  const [isWriting, setIsWriting] = useState(false);
  const [writeTimeout, setWriteTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      // Clean up timeout on unmount
      if (writeTimeout) {
        clearTimeout(writeTimeout);
      }
    };
  }, [writeTimeout]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setInputText(newText);
    
    // If user is typing, play quill scratch sound
    if (newText.length > inputText.length) {
      setIsWriting(true);
      playQuillScratch();
      
      // Reset the writing timeout
      if (writeTimeout) {
        clearTimeout(writeTimeout);
      }
      
      // Set a new timeout to mark when writing stops
      const timeout = setTimeout(() => {
        setIsWriting(false);
      }, 1000);
      
      setWriteTimeout(timeout);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputText.trim() || isSubmitting || isGeneratingResponse) return;

    setIsSubmitting(true);

    // Play ink absorption sound
    playInkAbsorption();

    try {
      // Add user entry (now async)
      await addUserEntry(inputText);

      // Animate text disappearing
      await new Promise(resolve => setTimeout(resolve, 1000));
      setInputText('');
    } catch (error) {
      console.error('Error submitting entry:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const inkedStyle = {
    fontFamily: 'cursive, serif',
    color: '#212121',
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="w-full">
        <div className="relative w-full">
          <textarea
            value={inputText}
            onChange={handleTextChange}
            style={inkedStyle}
            placeholder={isGeneratingResponse ? "Tom Riddle is writing..." : "Write your thoughts here..."}
            className={`w-full h-32 bg-transparent border-b border-[#4E342E] border-opacity-30 focus:outline-none focus:border-opacity-50 p-2 resize-none ${isSubmitting ? 'animate-fadeOut' : ''} ${isWriting ? 'writing-animation' : ''} ${isGeneratingResponse ? 'opacity-50' : ''}`}
            disabled={isSubmitting || isGeneratingResponse}
          />
          
          {/* Quill icon */}
          <motion.div 
            className="absolute bottom-2 right-2 text-[#4E342E] text-opacity-60"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <button
              type="submit"
              disabled={isSubmitting || isGeneratingResponse}
              className={`focus:outline-none hover:text-[#7D0A0A] transition-colors duration-300 hover-glow ${(isSubmitting || isGeneratingResponse) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="24" 
                height="24" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <path d="M20 2v20l-8-8 8-8z"></path>
                <path d="M4 22V2"></path>
              </svg>
            </button>
          </motion.div>
        </div>
      </form>
      
      <div className="mt-4 text-center">
        <p className="text-xs text-[#4E342E] text-opacity-40 italic">
          {isSubmitting
            ? "The ink fades into the page..."
            : isGeneratingResponse
            ? "Tom Riddle is writing a response..."
            : "Press enter or click the quill to submit"
          }
        </p>
      </div>
    </div>
  );
};

export default DiaryInput;